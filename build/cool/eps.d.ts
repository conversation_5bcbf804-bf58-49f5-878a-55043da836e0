declare namespace Eps {
	interface ChatgptSessionEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * UID
		 */
		uid?: number;
		/**
		 * 标题
		 */
		subject?: string;
		/**
		 * 消息ID
		 */
		lastMessageId?: string;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * Model
		 */
		model?: number;
		/**
		 * 人声类型
		 */
		voiceVal?: string;
		/**
		 * 最后一条消息
		 */
		lasMsgText?: string;
		/**
		 * 最后一条消息时间
		 */
		lastMsgTime?: number;
		/**
		 * 置顶
		 */
		digest?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DouyinUrlEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * UID
		 */
		uid?: number;
		/**
		 * Url
		 */
		url?: string;
		/**
		 * 标题
		 */
		title?: string;
		/**
		 * 作者
		 */
		author?: string;
		/**
		 * 视频创建时间
		 */
		videoDate?: number;
		/**
		 * 目标Url
		 */
		targetUrl?: string;
		/**
		 * 本地文件
		 */
		localFile?: string;
		/**
		 * 本地封面Url
		 */
		localCover?: string;
		/**
		 * 远程封面Url
		 */
		remoteCoverUrl?: string;
		/**
		 * 远程文件路径
		 */
		remoteFilePath?: string;
		/**
		 * 远程文件Url
		 */
		remoteFileUrl?: string;
		/**
		 * 类型
		 */
		type?: string;
		/**
		 * tingwu transId
		 */
		transId?: string;
		/**
		 * 文本
		 */
		txt?: string;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 跟踪ID
		 */
		followId?: number;
		/**
		 * 立即执行
		 */
		run?: number;
		/**
		 * 错误信息
		 */
		message?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface TblEvent {
		/**
		 * undefined
		 */
		id?: BigInt;
		/**
		 * undefined
		 */
		school_id?: BigInt;
		/**
		 * undefined
		 */
		subject?: string;
		/**
		 * undefined
		 */
		content?: string;
		/**
		 * undefined
		 */
		www_url?: number;
		/**
		 * undefined
		 */
		www_url_senduser?: number;
		/**
		 * undefined
		 */
		iese_url?: number;
		/**
		 * undefined
		 */
		apply_url?: number;
		/**
		 * undefined
		 */
		forum_url?: number;
		/**
		 * undefined
		 */
		forum_url_fid?: number;
		/**
		 * undefined
		 */
		forum_url_pid?: number;
		/**
		 * undefined
		 */
		forum_url_typeid?: number;
		/**
		 * undefined
		 */
		forum_url_senduser?: number;
		/**
		 * undefined
		 */
		highlight?: number;
		/**
		 * undefined
		 */
		digest?: number;
		/**
		 * undefined
		 */
		stick?: number;
		/**
		 * undefined
		 */
		push_0?: number;
		/**
		 * undefined
		 */
		push_1?: number;
		/**
		 * undefined
		 */
		push_2?: number;
		/**
		 * undefined
		 */
		push_3?: number;
		/**
		 * undefined
		 */
		status?: number;
		/**
		 * undefined
		 */
		total_display_count?: number;
		/**
		 * undefined
		 */
		total_redirect_count?: number;
		/**
		 * undefined
		 */
		created_at?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface CcForumThread {
		/**
		 * undefined
		 */
		tid?: number;
		/**
		 * undefined
		 */
		fid?: number;
		/**
		 * undefined
		 */
		posttableid?: smallint;
		/**
		 * undefined
		 */
		typeid?: smallint;
		/**
		 * undefined
		 */
		sortid?: smallint;
		/**
		 * undefined
		 */
		readperm?: number;
		/**
		 * undefined
		 */
		price?: smallint;
		/**
		 * undefined
		 */
		author?: char;
		/**
		 * undefined
		 */
		authorid?: number;
		/**
		 * undefined
		 */
		subject?: char;
		/**
		 * undefined
		 */
		dateline?: number;
		/**
		 * undefined
		 */
		lastpost?: number;
		/**
		 * undefined
		 */
		lastposter?: char;
		/**
		 * undefined
		 */
		views?: number;
		/**
		 * undefined
		 */
		replies?: number;
		/**
		 * undefined
		 */
		displayorder?: number;
		/**
		 * undefined
		 */
		highlight?: number;
		/**
		 * undefined
		 */
		digest?: number;
		/**
		 * undefined
		 */
		rate?: number;
		/**
		 * undefined
		 */
		special?: number;
		/**
		 * undefined
		 */
		attachment?: number;
		/**
		 * undefined
		 */
		moderated?: number;
		/**
		 * undefined
		 */
		closed?: number;
		/**
		 * undefined
		 */
		stickreply?: number;
		/**
		 * undefined
		 */
		recommends?: smallint;
		/**
		 * undefined
		 */
		recommend_add?: smallint;
		/**
		 * undefined
		 */
		recommend_sub?: smallint;
		/**
		 * undefined
		 */
		heats?: number;
		/**
		 * undefined
		 */
		status?: smallint;
		/**
		 * undefined
		 */
		isgroup?: number;
		/**
		 * undefined
		 */
		favtimes?: number;
		/**
		 * undefined
		 */
		sharetimes?: number;
		/**
		 * undefined
		 */
		stamp?: number;
		/**
		 * undefined
		 */
		icon?: number;
		/**
		 * undefined
		 */
		pushedaid?: number;
		/**
		 * undefined
		 */
		cover?: smallint;
		/**
		 * undefined
		 */
		replycredit?: smallint;
		/**
		 * undefined
		 */
		relatebytag?: char;
		/**
		 * undefined
		 */
		maxposition?: number;
		/**
		 * undefined
		 */
		bgcolor?: char;
		/**
		 * undefined
		 */
		comments?: number;
		/**
		 * undefined
		 */
		hidden?: smallint;
		/**
		 * undefined
		 */
		oldtypeid?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface Url {
		/**
		 * undefined
		 */
		ID?: number;
		/**
		 * undefined
		 */
		FatherID?: BigInt;
		/**
		 * undefined
		 */
		FatherPath?: string;
		/**
		 * undefined
		 */
		leaf?: bit;
		/**
		 * undefined
		 */
		iconCls?: string;
		/**
		 * undefined
		 */
		OriginalUrl?: string;
		/**
		 * undefined
		 */
		OriginalUrlPath?: string;
		/**
		 * undefined
		 */
		TargetUrl?: string;
		/**
		 * undefined
		 */
		CreateTime?: Date;
		/**
		 * undefined
		 */
		IsDeleted?: bit;
		/**
		 * undefined
		 */
		Describe?: string;
		/**
		 * undefined
		 */
		Enable?: bit;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface CommonHotspotEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 开始时间
		 */
		begin?: number;
		/**
		 * 结束时间
		 */
		end?: number;
		/**
		 * 标题
		 */
		title?: string;
		/**
		 * 目标地址
		 */
		targetUrl?: string;
		/**
		 * Referer
		 */
		referer?: string;
		/**
		 * 展示次数范围
		 */
		showTimeMin?: number;
		/**
		 * 展示次数范围
		 */
		showTimeMax?: number;
		/**
		 * 展示间隔范围
		 */
		showIntervalMin?: number;
		/**
		 * 展示间隔范围
		 */
		showIntervalMax?: number;
		/**
		 * 类型
		 */
		type?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BlogEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * UUID
		 */
		uuid?: string;
		/**
		 * 展示图
		 */
		pic?: string;
		/**
		 * 标题
		 */
		title?: string;
		/**
		 * 内容
		 */
		content?: string;
		/**
		 * 作者
		 */
		author?: string;
		/**
		 * 日期
		 */
		datetime?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PortalArticleEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * undefined
		 */
		pic?: string;
		/**
		 * undefined
		 */
		type?: number;
		/**
		 * undefined
		 */
		title?: string;
		/**
		 * undefined
		 */
		summary?: string;
		/**
		 * undefined
		 */
		content?: longtext;
		/**
		 * undefined
		 */
		datetime?: number;
		/**
		 * undefined
		 */
		author?: string;
		/**
		 * undefined
		 */
		hits?: number;
		/**
		 * undefined
		 */
		forumTid?: BigInt;
		/**
		 * undefined
		 */
		category?: string;
		/**
		 * undefined
		 */
		externalLink?: string;
		/**
		 * undefined
		 */
		status?: number;
		/**
		 * undefined
		 */
		displayOrder?: number;
		/**
		 * undefined
		 */
		tags?: string;
		/**
		 * undefined
		 */
		tagsVal?: string;
		/**
		 * undefined
		 */
		majorTags?: string;
		/**
		 * undefined
		 */
		majorTagsVal?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface CcCommonSearchLog {
		/**
		 * undefined
		 */
		id?: BigInt;
		/**
		 * undefined
		 */
		content?: string;
		/**
		 * undefined
		 */
		uid?: number;
		/**
		 * undefined
		 */
		username?: string;
		/**
		 * undefined
		 */
		ip?: char;
		/**
		 * undefined
		 */
		dateline?: number;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface CommonSougouEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 标题
		 */
		subject?: string;
		/**
		 * 内容
		 */
		content?: string;
		/**
		 * 类型
		 */
		type?: string;
		/**
		 * 文件名
		 */
		filename?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface WechatGroupEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 群ID
		 */
		roomId?: string;
		/**
		 * 群名称
		 */
		topic?: string;
		/**
		 * 微信ID
		 */
		wxId?: string;
		/**
		 * 微信昵称
		 */
		wxNickname?: string;
		/**
		 * 头像
		 */
		avatar?: string;
		/**
		 * 人数
		 */
		memberCount?: number;
		/**
		 * 状态
		 */
		status?: number;
		/**
		 * 最后一条消息
		 */
		lasMsgText?: string;
		/**
		 * 最后一条消息时间
		 */
		lastMsgTime?: number;
		/**
		 * 置顶
		 */
		digest?: number;
		/**
		 * 附件
		 */
		errorMessage?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysDepartmentEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 部门名称
		 */
		name?: string;
		/**
		 * 上级部门ID
		 */
		parentId?: number;
		/**
		 * 排序
		 */
		orderNum?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysLogEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 用户ID
		 */
		userId?: number;
		/**
		 * 行为
		 */
		action?: string;
		/**
		 * ip
		 */
		ip?: string;
		/**
		 * ip地址
		 */
		ipAddr?: string;
		/**
		 * 参数
		 */
		params?: json;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysMenuEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 父菜单ID
		 */
		parentId?: number;
		/**
		 * 菜单名称
		 */
		name?: string;
		/**
		 * 菜单地址
		 */
		router?: string;
		/**
		 * 权限标识
		 */
		perms?: string;
		/**
		 * 类型 0-目录 1-菜单 2-按钮
		 */
		type?: number;
		/**
		 * 图标
		 */
		icon?: string;
		/**
		 * 排序
		 */
		orderNum?: number;
		/**
		 * 视图地址
		 */
		viewPath?: string;
		/**
		 * 路由缓存
		 */
		keepAlive?: boolean;
		/**
		 * 是否显示
		 */
		isShow?: boolean;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysParamEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 键
		 */
		keyName?: string;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 数据
		 */
		data?: string;
		/**
		 * 数据类型 0-字符串 1-富文本 2-文件
		 */
		dataType?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysRoleEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 用户ID
		 */
		userId?: string;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 角色标签
		 */
		label?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 数据权限是否关联上下级
		 */
		relevance?: boolean;
		/**
		 * 菜单权限
		 */
		menuIdList?: json;
		/**
		 * 部门权限
		 */
		departmentIdList?: json;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface BaseSysUserEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 部门ID
		 */
		departmentId?: number;
		/**
		 * 姓名
		 */
		name?: string;
		/**
		 * 用户名
		 */
		username?: string;
		/**
		 * 密码
		 */
		password?: string;
		/**
		 * 密码版本, 作用是改完密码，让原来的token失效
		 */
		passwordV?: number;
		/**
		 * 昵称
		 */
		nickName?: string;
		/**
		 * 头像
		 */
		headImg?: string;
		/**
		 * 手机
		 */
		phone?: string;
		/**
		 * 邮箱
		 */
		email?: string;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 状态 0-禁用 1-启用
		 */
		status?: number;
		/**
		 * socketId
		 */
		socketId?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 类型ID
		 */
		typeId?: number;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 值
		 */
		value?: string;
		/**
		 * 排序
		 */
		orderNum?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 父ID
		 */
		parentId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface DictTypeEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 标识
		 */
		key?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface PluginInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * 简介
		 */
		description?: string;
		/**
		 * Key名
		 */
		keyName?: string;
		/**
		 * Hook
		 */
		hook?: string;
		/**
		 * 描述
		 */
		readme?: string;
		/**
		 * 版本
		 */
		version?: string;
		/**
		 * Logo(base64)
		 */
		logo?: string;
		/**
		 * 作者
		 */
		author?: string;
		/**
		 * 状态 0-禁用 1-启用
		 */
		status?: number;
		/**
		 * 内容
		 */
		content?: json;
		/**
		 * 插件的plugin.json
		 */
		pluginJson?: json;
		/**
		 * 配置
		 */
		config?: json;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface RecycleDataEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 表
		 */
		entityInfo?: json;
		/**
		 * 操作人
		 */
		userId?: number;
		/**
		 * 被删除的数据
		 */
		data?: json;
		/**
		 * 请求的接口
		 */
		url?: string;
		/**
		 * 请求参数
		 */
		params?: json;
		/**
		 * 删除数据条数
		 */
		count?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface SpaceInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 地址
		 */
		url?: string;
		/**
		 * 类型
		 */
		type?: string;
		/**
		 * 分类ID
		 */
		classifyId?: number;
		/**
		 * 文件id
		 */
		fileId?: string;
		/**
		 * 文件名
		 */
		name?: string;
		/**
		 * 文件大小
		 */
		size?: number;
		/**
		 * 文档版本
		 */
		version?: number;
		/**
		 * 文件位置
		 */
		key?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface SpaceTypeEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 类别名称
		 */
		name?: string;
		/**
		 * 父分类ID
		 */
		parentId?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface TaskInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 任务ID
		 */
		jobId?: string;
		/**
		 * 任务配置
		 */
		repeatConf?: string;
		/**
		 * 名称
		 */
		name?: string;
		/**
		 * cron
		 */
		cron?: string;
		/**
		 * 最大执行次数 不传为无限次
		 */
		limit?: number;
		/**
		 * 每间隔多少毫秒执行一次 如果cron设置了 这项设置就无效
		 */
		every?: number;
		/**
		 * 备注
		 */
		remark?: string;
		/**
		 * 状态 0-停止 1-运行
		 */
		status?: number;
		/**
		 * 开始时间
		 */
		startDate?: Date;
		/**
		 * 结束时间
		 */
		endDate?: Date;
		/**
		 * 数据
		 */
		data?: string;
		/**
		 * 执行的service实例ID
		 */
		service?: string;
		/**
		 * 状态 0-系统 1-用户
		 */
		type?: number;
		/**
		 * 下一次执行时间
		 */
		nextRunTime?: Date;
		/**
		 * 状态 0-cron 1-时间间隔
		 */
		taskType?: number;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface UserAddressEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 用户ID
		 */
		userId?: number;
		/**
		 * 联系人
		 */
		contact?: string;
		/**
		 * 手机号
		 */
		phone?: string;
		/**
		 * 省
		 */
		province?: string;
		/**
		 * 市
		 */
		city?: string;
		/**
		 * 区
		 */
		district?: string;
		/**
		 * 地址
		 */
		address?: string;
		/**
		 * 是否默认
		 */
		isDefault?: boolean;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}

	interface UserInfoEntity {
		/**
		 * ID
		 */
		id?: number;
		/**
		 * 登录唯一ID
		 */
		unionid?: string;
		/**
		 * 头像
		 */
		avatarUrl?: string;
		/**
		 * 昵称
		 */
		nickName?: string;
		/**
		 * 手机号
		 */
		phone?: string;
		/**
		 * 性别 0-未知 1-男 2-女
		 */
		gender?: number;
		/**
		 * 状态 0-禁用 1-正常 2-已注销
		 */
		status?: number;
		/**
		 * 登录方式 0-小程序 1-公众号 2-H5
		 */
		loginType?: number;
		/**
		 * 密码
		 */
		password?: string;
		/**
		 * 创建时间
		 */
		createTime?: Date;
		/**
		 * 更新时间
		 */
		updateTime?: Date;
		/**
		 * 任意键值
		 */
		[key: string]: any;
	}
	interface BaseComm {
		/**
		 * 修改个人信息
		 */
		personUpdate(data?: any): Promise<any>;
		/**
		 * 文件上传模式
		 */
		uploadMode(data?: any): Promise<any>;
		/**
		 * 权限与菜单
		 */
		permmenu(data?: any): Promise<any>;
		/**
		 * 个人信息
		 */
		person(data?: any): Promise<any>;
		/**
		 * 文件上传
		 */
		upload(data?: any): Promise<any>;
		/**
		 * 退出
		 */
		logout(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			personUpdate: string;
			uploadMode: string;
			permmenu: string;
			person: string;
			upload: string;
			logout: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			personUpdate: boolean;
			uploadMode: boolean;
			permmenu: boolean;
			person: boolean;
			upload: boolean;
			logout: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonChatgpt {
		/**
		 * 文生图
		 */
		generateImageFromText(data?: any): Promise<any>;
		/**
		 * GPTs验证用户更新
		 */
		gptsAuthUpdate(data?: any): Promise<any>;
		/**
		 * GPTs验证用户删除
		 */
		gptsAuthDelete(data?: any): Promise<any>;
		/**
		 * 物理删除会话
		 */
		destroySession(data?: any): Promise<any>;
		/**
		 * 更新标题
		 */
		updateSubject(data?: any): Promise<any>;
		/**
		 * 软删除会话
		 */
		deleteSession(data?: any): Promise<any>;
		/**
		 * AI供应商模型
		 */
		aiVenderModel(data?: any): Promise<any>;
		/**
		 * GPTs验证用户列表
		 */
		gptsAuthPage(data?: any): Promise<any>;
		/**
		 * GPTs验证用户添加
		 */
		gptsAuthAdd(data?: any): Promise<any>;
		/**
		 * 语音转文本
		 */
		voiceToText(data?: any): Promise<any>;
		/**
		 * 文转语音
		 */
		textToVoice(data?: any): Promise<any>;
		/**
		 * 解除绑定Tag
		 */
		unbindTag(data?: any): Promise<any>;
		/**
		 * 会话列表
		 */
		sessions(data?: any): Promise<any>;
		/**
		 * AI供应商
		 */
		aiVender(data?: any): Promise<any>;
		/**
		 * ChatGPT4.0
		 */
		chatgpt4(data?: any): Promise<any>;
		/**
		 * 绑定Tag
		 */
		bindTag(data?: any): Promise<any>;
		/**
		 * 问题列表
		 */
		details(data?: any): Promise<any>;
		/**
		 * 删除Tag
		 */
		delTag(data?: any): Promise<any>;
		/**
		 * 用户列表
		 */
		users(data?: any): Promise<any>;
		/**
		 * Tag列表
		 */
		tags(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<ChatgptSessionEntity>;
		/**
		 * 添加Tag
		 */
		tag(data?: any): Promise<any>;
		/**
		 * 获取Session对应Tag
		 */
		sessionid(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			generateImageFromText: string;
			gptsAuthUpdate: string;
			gptsAuthDelete: string;
			destroySession: string;
			updateSubject: string;
			deleteSession: string;
			aiVenderModel: string;
			gptsAuthPage: string;
			gptsAuthAdd: string;
			voiceToText: string;
			textToVoice: string;
			unbindTag: string;
			sessions: string;
			aiVender: string;
			chatgpt4: string;
			bindTag: string;
			details: string;
			delTag: string;
			users: string;
			tags: string;
			info: string;
			tag: string;
			sessionid: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			generateImageFromText: boolean;
			gptsAuthUpdate: boolean;
			gptsAuthDelete: boolean;
			destroySession: boolean;
			updateSubject: boolean;
			deleteSession: boolean;
			aiVenderModel: boolean;
			gptsAuthPage: boolean;
			gptsAuthAdd: boolean;
			voiceToText: boolean;
			textToVoice: boolean;
			unbindTag: boolean;
			sessions: boolean;
			aiVender: boolean;
			chatgpt4: boolean;
			bindTag: boolean;
			details: boolean;
			delTag: boolean;
			users: boolean;
			tags: boolean;
			info: boolean;
			tag: boolean;
			sessionid: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonDouyin {
		/**
		 * 抖音下载
		 */
		douyinUrlDownload(data?: any): Promise<any>;
		/**
		 * 抖音视频解析
		 */
		douyinUrlParse(data?: any): Promise<any>;
		/**
		 * 创建TagUser
		 */
		tagUserCreate(data?: any): Promise<any>;
		/**
		 * 删除TagUser
		 */
		tagUserDelete(data?: any): Promise<any>;
		/**
		 * 账号Cookie
		 */
		accountCookie(data?: any): Promise<any>;
		/**
		 * 创建账号
		 */
		accountCreate(data?: any): Promise<any>;
		/**
		 * 删除账号
		 */
		accountDelete(data?: any): Promise<any>;
		/**
		 * 主账号切换
		 */
		accountSwitch(data?: any): Promise<any>;
		/**
		 * 账号使能
		 */
		accountStatus(data?: any): Promise<any>;
		/**
		 * TagUser列表
		 */
		tagUserList(data?: any): Promise<any>;
		/**
		 * 检查QrCode
		 */
		checkQrCode(data?: any): Promise<any>;
		/**
		 * 用QrCode登录
		 */
		loginQrCode(data?: any): Promise<any>;
		/**
		 * 追踪用户对应的标签
		 */
		tagUserAll(data?: any): Promise<any>;
		/**
		 * 更新用户主页
		 */
		userUpdate(data?: any): Promise<any>;
		/**
		 * Home创建
		 */
		homeCreate(data?: any): Promise<any>;
		/**
		 * 抖音主页
		 */
		douyinHome(data?: any): Promise<any>;
		/**
		 * 创建Tag
		 */
		tagCreate(data?: any): Promise<any>;
		/**
		 * 修改Tag
		 */
		tagUpdate(data?: any): Promise<any>;
		/**
		 * 删除Tag
		 */
		tagDelete(data?: any): Promise<any>;
		/**
		 * 创建Url
		 */
		urlCreate(data?: any): Promise<any>;
		/**
		 * 修改Url
		 */
		urlUpdate(data?: any): Promise<any>;
		/**
		 * 获取Url对应图片
		 */
		urlImages(data?: any): Promise<any>;
		/**
		 * 删除Url
		 */
		urlDelete(data?: any): Promise<any>;
		/**
		 * QrCode Url
		 */
		qrCodeUrl(data?: any): Promise<any>;
		/**
		 * 用户主页列表
		 */
		userPage(data?: any): Promise<any>;
		/**
		 * Tag列表
		 */
		tagPage(data?: any): Promise<any>;
		/**
		 * Url列表
		 */
		urlPage(data?: any): Promise<any>;
		/**
		 * 重新处理Url
		 */
		urlRedo(data?: any): Promise<any>;
		/**
		 * 账号列表
		 */
		account(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<DouyinUrlEntity>;
		/**
		 * 权限标识
		 */
		permission: {
			douyinUrlDownload: string;
			douyinUrlParse: string;
			tagUserCreate: string;
			tagUserDelete: string;
			accountCookie: string;
			accountCreate: string;
			accountDelete: string;
			accountSwitch: string;
			accountStatus: string;
			tagUserList: string;
			checkQrCode: string;
			loginQrCode: string;
			tagUserAll: string;
			userUpdate: string;
			homeCreate: string;
			douyinHome: string;
			tagCreate: string;
			tagUpdate: string;
			tagDelete: string;
			urlCreate: string;
			urlUpdate: string;
			urlImages: string;
			urlDelete: string;
			qrCodeUrl: string;
			userPage: string;
			tagPage: string;
			urlPage: string;
			urlRedo: string;
			account: string;
			info: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			douyinUrlDownload: boolean;
			douyinUrlParse: boolean;
			tagUserCreate: boolean;
			tagUserDelete: boolean;
			accountCookie: boolean;
			accountCreate: boolean;
			accountDelete: boolean;
			accountSwitch: boolean;
			accountStatus: boolean;
			tagUserList: boolean;
			checkQrCode: boolean;
			loginQrCode: boolean;
			tagUserAll: boolean;
			userUpdate: boolean;
			homeCreate: boolean;
			douyinHome: boolean;
			tagCreate: boolean;
			tagUpdate: boolean;
			tagDelete: boolean;
			urlCreate: boolean;
			urlUpdate: boolean;
			urlImages: boolean;
			urlDelete: boolean;
			qrCodeUrl: boolean;
			userPage: boolean;
			tagPage: boolean;
			urlPage: boolean;
			urlRedo: boolean;
			account: boolean;
			info: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonEvent {
		/**
		 * updateTotalRedirectCount
		 */
		updateTotalRedirectCount(data?: any): Promise<any>;
		/**
		 * updateTotalDisplayCount
		 */
		updateTotalDisplayCount(data?: any): Promise<any>;
		/**
		 * push1WwwPositionUpdate
		 */
		push1WwwPositionUpdate(data?: any): Promise<any>;
		/**
		 * schoolCreateAndUpdate
		 */
		schoolCreateAndUpdate(data?: any): Promise<any>;
		/**
		 * userCreateAndUpdate
		 */
		userCreateAndUpdate(data?: any): Promise<any>;
		/**
		 * coreLocationDelete
		 */
		coreLocationDelete(data?: any): Promise<any>;
		/**
		 * pushLocationDelete
		 */
		pushLocationDelete(data?: any): Promise<any>;
		/**
		 * push1CountUpdate
		 */
		push1CountUpdate(data?: any): Promise<any>;
		/**
		 * push1WwwPosition
		 */
		push1WwwPosition(data?: any): Promise<any>;
		/**
		 * push2ComingSoon
		 */
		push2ComingSoon(data?: any): Promise<any>;
		/**
		 * calendarEditOne
		 */
		calendarEditOne(data?: any): Promise<any>;
		/**
		 * publishToApply
		 */
		publishToApply(data?: any): Promise<any>;
		/**
		 * releaseEditOne
		 */
		releaseEditOne(data?: any): Promise<any>;
		/**
		 * calendarDelete
		 */
		calendarDelete(data?: any): Promise<any>;
		/**
		 * materialDigest
		 */
		materialDigest(data?: any): Promise<any>;
		/**
		 * publishToIESE
		 */
		publishToIESE(data?: any): Promise<any>;
		/**
		 * majorCategory
		 */
		majorCategory(data?: any): Promise<any>;
		/**
		 * releaseDelete
		 */
		releaseDelete(data?: any): Promise<any>;
		/**
		 * getReleaseOne
		 */
		getReleaseOne(data?: any): Promise<any>;
		/**
		 * releaseCreate
		 */
		releaseCreate(data?: any): Promise<any>;
		/**
		 * externalsLog
		 */
		externalsLog(data?: any): Promise<any>;
		/**
		 * wwwHotUpdate
		 */
		wwwHotUpdate(data?: any): Promise<any>;
		/**
		 * calendarList
		 */
		calendarList(data?: any): Promise<any>;
		/**
		 * calendarEdit
		 */
		calendarEdit(data?: any): Promise<any>;
		/**
		 * calendarInfo
		 */
		calendarInfo(data?: any): Promise<any>;
		/**
		 * school_major
		 */
		school_major(data?: any): Promise<any>;
		/**
		 * changeOrder
		 */
		changeOrder(data?: any): Promise<any>;
		/**
		 * releaseList
		 */
		releaseList(data?: any): Promise<any>;
		/**
		 * release1To3
		 */
		release1To3(data?: any): Promise<any>;
		/**
		 * releaseEdit
		 */
		releaseEdit(data?: any): Promise<any>;
		/**
		 * getCalendar
		 */
		getCalendar(data?: any): Promise<any>;
		/**
		 * imageDelete
		 */
		imageDelete(data?: any): Promise<any>;
		/**
		 * imageDigest
		 */
		imageDigest(data?: any): Promise<any>;
		/**
		 * ieseUpdate
		 */
		ieseUpdate(data?: any): Promise<any>;
		/**
		 * ieseDelete
		 */
		ieseDelete(data?: any): Promise<any>;
		/**
		 * schoolList
		 */
		schoolList(data?: any): Promise<any>;
		/**
		 * userDelete
		 */
		userDelete(data?: any): Promise<any>;
		/**
		 * push1Count
		 */
		push1Count(data?: any): Promise<any>;
		/**
		 * externals
		 */
		externals(data?: any): Promise<any>;
		/**
		 * ieseList
		 */
		ieseList(data?: any): Promise<any>;
		/**
		 * acUpdate
		 */
		acUpdate(data?: any): Promise<any>;
		/**
		 * userList
		 */
		userList(data?: any): Promise<any>;
		/**
		 * push1Add
		 */
		push1Add(data?: any): Promise<any>;
		/**
		 * calendar
		 */
		calendar(data?: any): Promise<any>;
		/**
		 * ieseAdd
		 */
		ieseAdd(data?: any): Promise<any>;
		/**
		 * offline
		 */
		offline(data?: any): Promise<any>;
		/**
		 * urlPage
		 */
		urlPage(data?: any): Promise<any>;
		/**
		 * acList
		 */
		acList(data?: any): Promise<any>;
		/**
		 * wwwHot
		 */
		wwwHot(data?: any): Promise<any>;
		/**
		 * getGeo
		 */
		getGeo(data?: any): Promise<any>;
		/**
		 * remove
		 */
		remove(data?: any): Promise<any>;
		/**
		 * create
		 */
		create(data?: any): Promise<any>;
		/**
		 * core
		 */
		core(data?: any): Promise<any>;
		/**
		 * edit
		 */
		edit(data?: any): Promise<any>;
		/**
		 * type
		 */
		type(data?: any): Promise<any>;
		/**
		 * orgs
		 */
		orgs(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<TblEvent>;
		/**
		 * geo
		 */
		geo(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			updateTotalRedirectCount: string;
			updateTotalDisplayCount: string;
			push1WwwPositionUpdate: string;
			schoolCreateAndUpdate: string;
			userCreateAndUpdate: string;
			coreLocationDelete: string;
			pushLocationDelete: string;
			push1CountUpdate: string;
			push1WwwPosition: string;
			push2ComingSoon: string;
			calendarEditOne: string;
			publishToApply: string;
			releaseEditOne: string;
			calendarDelete: string;
			materialDigest: string;
			publishToIESE: string;
			majorCategory: string;
			releaseDelete: string;
			getReleaseOne: string;
			releaseCreate: string;
			externalsLog: string;
			wwwHotUpdate: string;
			calendarList: string;
			calendarEdit: string;
			calendarInfo: string;
			school_major: string;
			changeOrder: string;
			releaseList: string;
			release1To3: string;
			releaseEdit: string;
			getCalendar: string;
			imageDelete: string;
			imageDigest: string;
			ieseUpdate: string;
			ieseDelete: string;
			schoolList: string;
			userDelete: string;
			push1Count: string;
			externals: string;
			ieseList: string;
			acUpdate: string;
			userList: string;
			push1Add: string;
			calendar: string;
			ieseAdd: string;
			offline: string;
			urlPage: string;
			acList: string;
			wwwHot: string;
			getGeo: string;
			remove: string;
			create: string;
			core: string;
			edit: string;
			type: string;
			orgs: string;
			info: string;
			geo: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			updateTotalRedirectCount: boolean;
			updateTotalDisplayCount: boolean;
			push1WwwPositionUpdate: boolean;
			schoolCreateAndUpdate: boolean;
			userCreateAndUpdate: boolean;
			coreLocationDelete: boolean;
			pushLocationDelete: boolean;
			push1CountUpdate: boolean;
			push1WwwPosition: boolean;
			push2ComingSoon: boolean;
			calendarEditOne: boolean;
			publishToApply: boolean;
			releaseEditOne: boolean;
			calendarDelete: boolean;
			materialDigest: boolean;
			publishToIESE: boolean;
			majorCategory: boolean;
			releaseDelete: boolean;
			getReleaseOne: boolean;
			releaseCreate: boolean;
			externalsLog: boolean;
			wwwHotUpdate: boolean;
			calendarList: boolean;
			calendarEdit: boolean;
			calendarInfo: boolean;
			school_major: boolean;
			changeOrder: boolean;
			releaseList: boolean;
			release1To3: boolean;
			releaseEdit: boolean;
			getCalendar: boolean;
			imageDelete: boolean;
			imageDigest: boolean;
			ieseUpdate: boolean;
			ieseDelete: boolean;
			schoolList: boolean;
			userDelete: boolean;
			push1Count: boolean;
			externals: boolean;
			ieseList: boolean;
			acUpdate: boolean;
			userList: boolean;
			push1Add: boolean;
			calendar: boolean;
			ieseAdd: boolean;
			offline: boolean;
			urlPage: boolean;
			acList: boolean;
			wwwHot: boolean;
			getGeo: boolean;
			remove: boolean;
			create: boolean;
			core: boolean;
			edit: boolean;
			type: boolean;
			orgs: boolean;
			info: boolean;
			geo: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonForum {
		/**
		 * 导出敏感内容到Excel文件
		 */
		exportSensitiveContentToExcel(data?: any): Promise<any>;
		/**
		 * 搜索推荐分组更新
		 */
		searchRecommendGroupUpdate(data?: any): Promise<any>;
		/**
		 * 搜索推荐分组删除
		 */
		searchRecommendGroupDelete(data?: any): Promise<any>;
		/**
		 * 初始化搜索推荐索引
		 */
		initSearchRecommandIndex(data?: any): Promise<any>;
		/**
		 * 搜索推荐分组列表
		 */
		searchRecommendGroupList(data?: any): Promise<any>;
		/**
		 * 搜索推荐分组列表
		 */
		searchRecommendGroupPage(data?: any): Promise<any>;
		/**
		 * 搜索推荐分组添加
		 */
		searchRecommendGroupAdd(data?: any): Promise<any>;
		/**
		 * sensitiveMonitorUpdate
		 */
		sensitiveMonitorUpdate(data?: any): Promise<any>;
		/**
		 * 搜索推荐更新
		 */
		searchRecommendUpdate(data?: any): Promise<any>;
		/**
		 * 搜索推荐删除
		 */
		searchRecommendDelete(data?: any): Promise<any>;
		/**
		 * 搜索推荐列表
		 */
		searchRecommendPage(data?: any): Promise<any>;
		/**
		 * sensitiveMonitorGet
		 */
		sensitiveMonitorGet(data?: any): Promise<any>;
		/**
		 * 初始化论坛数据
		 */
		initForumIndexData(data?: any): Promise<any>;
		/**
		 * 搜索推荐添加
		 */
		searchRecommendAdd(data?: any): Promise<any>;
		/**
		 * 论坛帖子权重删除
		 */
		threadWeightDelete(data?: any): Promise<any>;
		/**
		 * getThreadUrlByPid
		 */
		getThreadUrlByPid(data?: any): Promise<any>;
		/**
		 * updateShowMobile
		 */
		updateShowMobile(data?: any): Promise<any>;
		/**
		 * appFeatureSwitch
		 */
		appFeatureSwitch(data?: any): Promise<any>;
		/**
		 * 论坛帖子权重列表
		 */
		threadWeightPage(data?: any): Promise<any>;
		/**
		 * 修复论坛快速回复帖子数据
		 */
		fixForumPostData(data?: any): Promise<any>;
		/**
		 * highlightDigest
		 */
		highlightDigest(data?: any): Promise<any>;
		/**
		 * 论坛帖子权重添加
		 */
		threadWeightAdd(data?: any): Promise<any>;
		/**
		 * publishToForum
		 */
		publishToForum(data?: any): Promise<any>;
		/**
		 * 初始化论坛索引
		 */
		initForumIndex(data?: any): Promise<any>;
		/**
		 * getShowMobile
		 */
		getShowMobile(data?: any): Promise<any>;
		/**
		 * publishToWWW
		 */
		publishToWWW(data?: any): Promise<any>;
		/**
		 * 论坛帖子权重
		 */
		threadWeight(data?: any): Promise<any>;
		/**
		 * sensitive
		 */
		sensitive(data?: any): Promise<any>;
		/**
		 * subnav
		 */
		subnav(data?: any): Promise<any>;
		/**
		 * stick
		 */
		stick(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<CcForumThread>;
		/**
		 * nav
		 */
		nav(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			exportSensitiveContentToExcel: string;
			searchRecommendGroupUpdate: string;
			searchRecommendGroupDelete: string;
			initSearchRecommandIndex: string;
			searchRecommendGroupList: string;
			searchRecommendGroupPage: string;
			searchRecommendGroupAdd: string;
			sensitiveMonitorUpdate: string;
			searchRecommendUpdate: string;
			searchRecommendDelete: string;
			searchRecommendPage: string;
			sensitiveMonitorGet: string;
			initForumIndexData: string;
			searchRecommendAdd: string;
			threadWeightDelete: string;
			getThreadUrlByPid: string;
			updateShowMobile: string;
			appFeatureSwitch: string;
			threadWeightPage: string;
			fixForumPostData: string;
			highlightDigest: string;
			threadWeightAdd: string;
			publishToForum: string;
			initForumIndex: string;
			getShowMobile: string;
			publishToWWW: string;
			threadWeight: string;
			sensitive: string;
			subnav: string;
			stick: string;
			info: string;
			nav: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			exportSensitiveContentToExcel: boolean;
			searchRecommendGroupUpdate: boolean;
			searchRecommendGroupDelete: boolean;
			initSearchRecommandIndex: boolean;
			searchRecommendGroupList: boolean;
			searchRecommendGroupPage: boolean;
			searchRecommendGroupAdd: boolean;
			sensitiveMonitorUpdate: boolean;
			searchRecommendUpdate: boolean;
			searchRecommendDelete: boolean;
			searchRecommendPage: boolean;
			sensitiveMonitorGet: boolean;
			initForumIndexData: boolean;
			searchRecommendAdd: boolean;
			threadWeightDelete: boolean;
			getThreadUrlByPid: boolean;
			updateShowMobile: boolean;
			appFeatureSwitch: boolean;
			threadWeightPage: boolean;
			fixForumPostData: boolean;
			highlightDigest: boolean;
			threadWeightAdd: boolean;
			publishToForum: boolean;
			initForumIndex: boolean;
			getShowMobile: boolean;
			publishToWWW: boolean;
			threadWeight: boolean;
			sensitive: boolean;
			subnav: boolean;
			stick: boolean;
			info: boolean;
			nav: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonGo {
		/**
		 * 删除Statistic
		 */
		urlStatisticsDel(data?: any): Promise<any>;
		/**
		 * 获取Statistic
		 */
		statisticByUrl(data?: any): Promise<any>;
		/**
		 * 获取Statistic
		 */
		detailByUrl(data?: any): Promise<any>;
		/**
		 * 获取QueryGroup
		 */
		queryGroup(data?: any): Promise<any>;
		/**
		 * Url更新
		 */
		urlUpdate(data?: any): Promise<any>;
		/**
		 * Url删除
		 */
		urlDelete(data?: any): Promise<any>;
		/**
		 * Url列表
		 */
		urlPage(data?: any): Promise<any>;
		/**
		 * 根据ID获取Url
		 */
		urlByID(data?: any): Promise<any>;
		/**
		 * 获取Url子节点
		 */
		urlSub(data?: any): Promise<any>;
		/**
		 * Url添加
		 */
		urlAdd(data?: any): Promise<any>;
		/**
		 * Url状态更新
		 */
		update(data?: any): Promise<any>;
		/**
		 * 导出Excel
		 */
		export(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<Url>;
		/**
		 * 权限标识
		 */
		permission: {
			urlStatisticsDel: string;
			statisticByUrl: string;
			detailByUrl: string;
			queryGroup: string;
			urlUpdate: string;
			urlDelete: string;
			urlPage: string;
			urlByID: string;
			urlSub: string;
			urlAdd: string;
			update: string;
			export: string;
			info: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			urlStatisticsDel: boolean;
			statisticByUrl: boolean;
			detailByUrl: boolean;
			queryGroup: boolean;
			urlUpdate: boolean;
			urlDelete: boolean;
			urlPage: boolean;
			urlByID: boolean;
			urlSub: boolean;
			urlAdd: boolean;
			update: boolean;
			export: boolean;
			info: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonHotspot {
		/**
		 * 创建Hotspot
		 */
		hotspotCreate(data?: any): Promise<any>;
		/**
		 * 编辑Hotspot
		 */
		hotspotUpdate(data?: any): Promise<any>;
		/**
		 * 删除Hotspot
		 */
		hotspotDelete(data?: any): Promise<any>;
		/**
		 * HotSpot列表
		 */
		hotSpotPage(data?: any): Promise<any>;
		/**
		 * 添加Hotspot
		 */
		hotspotAdd(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			hotspotCreate: string;
			hotspotUpdate: string;
			hotspotDelete: string;
			hotSpotPage: string;
			hotspotAdd: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			hotspotCreate: boolean;
			hotspotUpdate: boolean;
			hotspotDelete: boolean;
			hotSpotPage: boolean;
			hotspotAdd: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonIese {
		/**
		 * 查找指定数据
		 */
		findOne(data?: any): Promise<any>;
		/**
		 * 创建
		 */
		create(data?: any): Promise<any>;
		/**
		 * 列表
		 */
		pages(data?: any): Promise<any>;
		/**
		 * 编辑
		 */
		edit(data?: any): Promise<any>;
		/**
		 * 类型列表
		 */
		tags(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BlogEntity>;
		/**
		 * 删除
		 */
		del(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			findOne: string;
			create: string;
			pages: string;
			edit: string;
			tags: string;
			info: string;
			del: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			findOne: boolean;
			create: boolean;
			pages: boolean;
			edit: boolean;
			tags: boolean;
			info: boolean;
			del: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonPortal {
		/**
		 * 获取 Portal Banner 索引
		 */
		getPortalBannerIndex(data?: any): Promise<any>;
		/**
		 * 设置 Portal Banner 索引
		 */
		setPortalBannerIndex(data?: any): Promise<any>;
		/**
		 * 更新文章显示顺序
		 */
		updateDisplayOrder(data?: any): Promise<any>;
		/**
		 * 获取可用标签
		 */
		getAvailableTags(data?: any): Promise<any>;
		/**
		 * Portal FindOne
		 */
		portalFindOne(data?: any): Promise<any>;
		/**
		 * 创建Portal
		 */
		portalCreate(data?: any): Promise<any>;
		/**
		 * 编辑Portal
		 */
		portalUpdate(data?: any): Promise<any>;
		/**
		 * 删除Portal
		 */
		portalDelete(data?: any): Promise<any>;
		/**
		 * Portal列表
		 */
		portalPage(data?: any): Promise<any>;
		/**
		 * 搜索标签
		 */
		tagSearch(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			getPortalBannerIndex: string;
			setPortalBannerIndex: string;
			updateDisplayOrder: string;
			getAvailableTags: string;
			portalFindOne: string;
			portalCreate: string;
			portalUpdate: string;
			portalDelete: string;
			portalPage: string;
			tagSearch: string;
			update: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			getPortalBannerIndex: boolean;
			setPortalBannerIndex: boolean;
			updateDisplayOrder: boolean;
			getAvailableTags: boolean;
			portalFindOne: boolean;
			portalCreate: boolean;
			portalUpdate: boolean;
			portalDelete: boolean;
			portalPage: boolean;
			tagSearch: boolean;
			update: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonSearchLog {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<CcCommonSearchLog>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<CcCommonSearchLog[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: CcCommonSearchLog[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonSougou {
		/**
		 * 初始化索引
		 */
		initIndex(data?: any): Promise<any>;
		/**
		 * 初始化数据
		 */
		initData(data?: any): Promise<any>;
		/**
		 * 创建
		 */
		create(data?: any): Promise<any>;
		/**
		 * 搜索
		 */
		search(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 查询相邻数据
		 */
		more(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			initIndex: string;
			initData: string;
			create: string;
			search: string;
			update: string;
			more: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			initIndex: boolean;
			initData: boolean;
			create: boolean;
			search: boolean;
			update: boolean;
			more: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseCommonWechat {
		/**
		 * 编辑组信息
		 */
		updateGroup(data?: any): Promise<any>;
		/**
		 * 微信信息
		 */
		wechatInfo(data?: any): Promise<any>;
		/**
		 * 获取登录二维码
		 */
		getQrCode(data?: any): Promise<any>;
		/**
		 * 消息列表
		 */
		messages(data?: any): Promise<any>;
		/**
		 * 退出登录
		 */
		logout(data?: any): Promise<any>;
		/**
		 * 聊天列表
		 */
		list(data?: any): Promise<WechatGroupEntity[]>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<WechatGroupEntity>;
		/**
		 * 权限标识
		 */
		permission: {
			updateGroup: string;
			wechatInfo: string;
			getQrCode: string;
			messages: string;
			logout: string;
			list: string;
			info: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			updateGroup: boolean;
			wechatInfo: boolean;
			getQrCode: boolean;
			messages: boolean;
			logout: boolean;
			list: boolean;
			info: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseOpen {
		/**
		 * upload4EventSchoolLogo
		 */
		upload4EventSchoolLogo(data?: any): Promise<any>;
		/**
		 * push1_www_position
		 */
		push1_www_position(data?: any): Promise<any>;
		/**
		 * upload4EventPush2
		 */
		upload4EventPush2(data?: any): Promise<any>;
		/**
		 * upload4PortalPic
		 */
		upload4PortalPic(data?: any): Promise<any>;
		/**
		 * appFeatureSwitch
		 */
		appFeatureSwitch(data?: any): Promise<any>;
		/**
		 * 检查论坛登录状态
		 */
		checkForumLogin(data?: any): Promise<any>;
		/**
		 * searchRecommend
		 */
		searchRecommend(data?: any): Promise<any>;
		/**
		 * upload4IESEPic
		 */
		upload4IESEPic(data?: any): Promise<any>;
		/**
		 * upload4Wechat
		 */
		upload4Wechat(data?: any): Promise<any>;
		/**
		 * 刷新token
		 */
		refreshToken(data?: any): Promise<any>;
		/**
		 * upload4Event
		 */
		upload4Event(data?: any): Promise<any>;
		/**
		 * showCalendar
		 */
		showCalendar(data?: any): Promise<any>;
		/**
		 * searchItems
		 */
		searchItems(data?: any): Promise<any>;
		/**
		 * showRelease
		 */
		showRelease(data?: any): Promise<any>;
		/**
		 * ieseEvents
		 */
		ieseEvents(data?: any): Promise<any>;
		/**
		 * upload4GPT
		 */
		upload4GPT(data?: any): Promise<any>;
		/**
		 * ieseBlogs
		 */
		ieseBlogs(data?: any): Promise<any>;
		/**
		 * sendmail
		 */
		sendmail(data?: any): Promise<any>;
		/**
		 * GPTs验证
		 */
		gptsAuth(data?: any): Promise<any>;
		/**
		 * 验证码
		 */
		captcha(data?: any): Promise<any>;
		/**
		 * Hotspot列表
		 */
		hotspot(data?: any): Promise<any>;
		/**
		 * www_hot
		 */
		www_hot(data?: any): Promise<any>;
		/**
		 * search
		 */
		search(data?: any): Promise<any>;
		/**
		 * wwwCDG
		 */
		wwwCDG(data?: any): Promise<any>;
		/**
		 * 登录
		 */
		login(data?: any): Promise<any>;
		/**
		 * wecom
		 */
		wecom(data?: any): Promise<any>;
		/**
		 * wecom
		 */
		wecom(data?: any): Promise<any>;
		/**
		 * 获得网页内容的参数值
		 */
		html(data?: any): Promise<any>;
		/**
		 * 实体信息与路径
		 */
		eps(data?: any): Promise<any>;
		/**
		 * ttt
		 */
		ttt(data?: any): Promise<any>;
		/**
		 * img
		 */
		img(data?: any): Promise<any>;
		/**
		 * file
		 */
		file(data?: any): Promise<any>;
		/**
		 * id
		 */
		id(data?: any): Promise<any>;
		/**
		 * id
		 */
		id(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			upload4EventSchoolLogo: string;
			push1_www_position: string;
			upload4EventPush2: string;
			upload4PortalPic: string;
			appFeatureSwitch: string;
			checkForumLogin: string;
			searchRecommend: string;
			upload4IESEPic: string;
			upload4Wechat: string;
			refreshToken: string;
			upload4Event: string;
			showCalendar: string;
			searchItems: string;
			showRelease: string;
			ieseEvents: string;
			upload4GPT: string;
			ieseBlogs: string;
			sendmail: string;
			gptsAuth: string;
			captcha: string;
			hotspot: string;
			www_hot: string;
			search: string;
			wwwCDG: string;
			login: string;
			wecom: string;
			wecom: string;
			html: string;
			eps: string;
			ttt: string;
			img: string;
			file: string;
			id: string;
			id: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			upload4EventSchoolLogo: boolean;
			push1_www_position: boolean;
			upload4EventPush2: boolean;
			upload4PortalPic: boolean;
			appFeatureSwitch: boolean;
			checkForumLogin: boolean;
			searchRecommend: boolean;
			upload4IESEPic: boolean;
			upload4Wechat: boolean;
			refreshToken: boolean;
			upload4Event: boolean;
			showCalendar: boolean;
			searchItems: boolean;
			showRelease: boolean;
			ieseEvents: boolean;
			upload4GPT: boolean;
			ieseBlogs: boolean;
			sendmail: boolean;
			gptsAuth: boolean;
			captcha: boolean;
			hotspot: boolean;
			www_hot: boolean;
			search: boolean;
			wwwCDG: boolean;
			login: boolean;
			wecom: boolean;
			wecom: boolean;
			html: boolean;
			eps: boolean;
			ttt: boolean;
			img: boolean;
			file: boolean;
			id: boolean;
			id: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysDepartment {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 排序
		 */
		order(data?: any): Promise<any>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysDepartmentEntity[]>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: { delete: string; update: string; order: string; list: string; add: string };
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			order: boolean;
			list: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysLog {
		/**
		 * 日志保存时间
		 */
		setKeep(data?: any): Promise<any>;
		/**
		 * 获得日志保存时间
		 */
		getKeep(data?: any): Promise<any>;
		/**
		 * 清理
		 */
		clear(data?: any): Promise<any>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysLogEntity[];
			[key: string]: any;
		}>;
		/**
		 * 权限标识
		 */
		permission: { setKeep: string; getKeep: string; clear: string; page: string };
		/**
		 * 权限状态
		 */
		_permission: { setKeep: boolean; getKeep: boolean; clear: boolean; page: boolean };
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysMenu {
		/**
		 * 创建代码
		 */
		create(data?: any): Promise<any>;
		/**
		 * 导出
		 */
		export(data?: any): Promise<any>;
		/**
		 * 导入
		 */
		import(data?: any): Promise<any>;
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 解析
		 */
		parse(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysMenuEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysMenuEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysMenuEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			create: string;
			export: string;
			import: string;
			delete: string;
			update: string;
			parse: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			create: boolean;
			export: boolean;
			import: boolean;
			delete: boolean;
			update: boolean;
			parse: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysParam {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 获得网页内容的参数值
		 */
		html(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysParamEntity>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysParamEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			html: string;
			info: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			html: boolean;
			info: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysRole {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysRoleEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysRoleEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysRoleEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface BaseSysUser {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 移动部门
		 */
		move(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<BaseSysUserEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<BaseSysUserEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: BaseSysUserEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			move: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			move: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface DictInfo {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 获得字典数据
		 */
		data(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<DictInfoEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<DictInfoEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: DictInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			data: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			data: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface DictType {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<DictTypeEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<DictTypeEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: DictTypeEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface PluginInfo {
		/**
		 * 安装插件
		 */
		install(data?: any): Promise<any>;
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<PluginInfoEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<PluginInfoEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: PluginInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			install: string;
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			install: boolean;
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface RecycleData {
		/**
		 * 恢复数据
		 */
		restore(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<RecycleDataEntity>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: RecycleDataEntity[];
			[key: string]: any;
		}>;
		/**
		 * 权限标识
		 */
		permission: { restore: string; info: string; page: string };
		/**
		 * 权限状态
		 */
		_permission: { restore: boolean; info: boolean; page: boolean };
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface SpaceInfo {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<SpaceInfoEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<SpaceInfoEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: SpaceInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface SpaceType {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<SpaceTypeEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<SpaceTypeEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: SpaceTypeEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface TaskInfo {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 开始
		 */
		start(data?: any): Promise<any>;
		/**
		 * 执行一次
		 */
		once(data?: any): Promise<any>;
		/**
		 * 停止
		 */
		stop(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<TaskInfoEntity>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: TaskInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * 日志
		 */
		log(data?: any): Promise<any>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			start: string;
			once: string;
			stop: string;
			info: string;
			page: string;
			log: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			start: boolean;
			once: boolean;
			stop: boolean;
			info: boolean;
			page: boolean;
			log: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface UserAddress {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<UserAddressEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<UserAddressEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: UserAddressEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	interface UserInfo {
		/**
		 * 删除
		 */
		delete(data?: any): Promise<any>;
		/**
		 * 修改
		 */
		update(data?: any): Promise<any>;
		/**
		 * 单个信息
		 */
		info(data?: any): Promise<UserInfoEntity>;
		/**
		 * 列表查询
		 */
		list(data?: any): Promise<UserInfoEntity[]>;
		/**
		 * 分页查询
		 */
		page(data?: any): Promise<{
			pagination: { size: number; page: number; total: number; [key: string]: any };
			list: UserInfoEntity[];
			[key: string]: any;
		}>;
		/**
		 * 新增
		 */
		add(data?: any): Promise<any>;
		/**
		 * 权限标识
		 */
		permission: {
			delete: string;
			update: string;
			info: string;
			list: string;
			page: string;
			add: string;
		};
		/**
		 * 权限状态
		 */
		_permission: {
			delete: boolean;
			update: boolean;
			info: boolean;
			list: boolean;
			page: boolean;
			add: boolean;
		};
		/**
		 * 请求
		 */
		request: Service["request"];
	}

	type json = any;

	type Service = {
		request(options?: {
			url: string;
			method?: "POST" | "GET" | "PUT" | "DELETE" | "PATCH" | "HEAD" | "OPTIONS";
			data?: any;
			params?: any;
			headers?: {
				[key: string]: any;
			};
			timeout?: number;
			proxy?: boolean;
			[key: string]: any;
		}): Promise<any>;
		base: {
			comm: BaseComm;
			common: {
				chatgpt: BaseCommonChatgpt;
				douyin: BaseCommonDouyin;
				event: BaseCommonEvent;
				forum: BaseCommonForum;
				go: BaseCommonGo;
				hotspot: BaseCommonHotspot;
				iese: BaseCommonIese;
				portal: BaseCommonPortal;
				searchLog: BaseCommonSearchLog;
				sougou: BaseCommonSougou;
				wechat: BaseCommonWechat;
			};
			open: BaseOpen;
			sys: {
				department: BaseSysDepartment;
				log: BaseSysLog;
				menu: BaseSysMenu;
				param: BaseSysParam;
				role: BaseSysRole;
				user: BaseSysUser;
			};
		};
		dict: { info: DictInfo; type: DictType };
		plugin: { info: PluginInfo };
		recycle: { data: RecycleData };
		space: { info: SpaceInfo; type: SpaceType };
		task: { info: TaskInfo };
		user: { address: UserAddress; info: UserInfo };
	};
}
