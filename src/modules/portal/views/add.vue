<template>
	<div class="wrapper">
		<div class="form-container">
			<el-form :model="form" label-width="100px" :rules="rules" ref="formRef">
				<el-form-item label="展示图" :required="true" prop="pic">
					<div v-if="form.pic?.length > 0" class="material">
							<el-image
								style="width: 120px; height: 80px"
								:src="form.pic"
								fit="cover"
							/>
							<el-icon class="del" size="20" @click="delPic()"
								><Delete
							/></el-icon>
						</div>
						<el-icon
							v-else
							class="cursor-pointer"
							size="30"
							color="#CFD3DC"
							@click="addPic()"
							><CirclePlus
						/></el-icon>
						<input
							type="file"
							multiple
							accept="image/*"
							:ref="setRefs('fileInput')"
							style="display: none"
							@change="handleFileChange($event, 1)"
						/>
				</el-form-item>

				<el-form-item label="类型" :required="true" prop="type">
					<el-radio-group v-model="form.type">
						<el-radio :label="0">标题摘要</el-radio>
						<el-radio :label="1">通栏图片</el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="标题" :required="true" prop="title">
					<el-input v-model="form.title" placeholder="请输入标题" />
				</el-form-item>

				<el-form-item label="摘要" :required="true" prop="summary">
					<el-input
						type="textarea"
						v-model="form.summary"
						placeholder="请输入摘要"
						rows="3"
					/>
				</el-form-item>

				<el-form-item label="外链">
					<div style="display: flex; align-items: center; gap: 8px; width: 100%;">
						<el-checkbox v-model="form.isExternalLink" @change="handleExternalLinkChange" style="flex-shrink: 0;" />
						<el-input
							v-model="form.externalLink"
							placeholder="请输入外链地址"
							:disabled="!form.isExternalLink"
							style="flex: 1; min-width: 0;"
						/>
					</div>
				</el-form-item>

				<el-form-item label="内容" :required="!form.isExternalLink" prop="content">
					<div id="vditor" class="vditor-container"></div>
					<div v-if="form.isExternalLink" class="external-link-overlay">
						<span>启用外链时，内容编辑器不可用</span>
					</div>
				</el-form-item>

				<el-form-item label="Thread ID" :required="!form.thread && !form.isExternalLink" prop="forumTid">
					<el-input v-model="form.forumTid" placeholder="请输入帖子链接或帖子ID，例如: https://forum.chasedream.com/thread-1397043-1-1.html" clearable :disabled="form.isExternalLink" /><br />
					<el-checkbox v-model="form.thread" label="新建帖子" size="large" :disabled="form.isExternalLink" />
				</el-form-item>

				<el-form-item label="帖子版块" v-if="form.thread">
					<el-row :gutter="20" style="width: 100%">
						<el-col :span="8">
							<el-form-item prop="fid">
								<el-select
									v-model="form.fid"
									placeholder="请选择"
									@change="navChange"
								>
									<el-option-group
										v-for="group in navs"
										:key="group.fid"
										:label="group.name"
									>
										<el-option
											v-for="item in group.forums"
											:key="item.fid"
											:label="item.name"
											:value="item.fid"
										/>
									</el-option-group>
								</el-select>
							</el-form-item>
						</el-col>
						<el-col :span="8">
							<el-form-item prop="typeid">
								<el-select v-model="form.typeid" placeholder="请选择">
									<el-option
										v-for="obj in subNavs"
										:key="obj.typeid"
										:label="obj.name"
										:value="obj.typeid"
									/>
								</el-select>
							</el-form-item>
						</el-col>
					</el-row>
				</el-form-item>
				<el-form-item label="发帖账号" prop="uid" v-if="form.thread">
					<el-radio-group v-model="form.uid" @change="uidChange">
						<el-radio
							v-for="obj in forumAccount"
							:key="obj.uid"
							:label="obj.username"
							:value="obj.uid"
						></el-radio>
					</el-radio-group>
				</el-form-item>

				<el-form-item label="作者" :required="!form.isExternalLink" prop="author">
					<el-input v-model="form.author" placeholder="请输入作者" :disabled="form.isExternalLink" />
				</el-form-item>

				<el-form-item label="分类" :required="true" prop="category">
					<el-radio-group v-model="form.category">
						<el-radio label="MBA">MBA</el-radio>
						<el-radio label="GMAT">GMAT</el-radio>
						<el-radio label="Master">Master</el-radio>
						<el-radio label="PhD">PhD</el-radio>						
					</el-radio-group>
				</el-form-item>

				<el-form-item label="排序" prop="displayOrder">
					<el-select v-model="form.displayOrder" placeholder="请选择排序">
						<el-option :value="3" label="3" />
						<el-option :value="2" label="2" />
						<el-option :value="1" label="1" />
						<el-option :value="0" label="0" />
					</el-select>
				</el-form-item>

				<el-form-item label="标签" prop="tags">
					<div class="tags-container-combined">
						<div class="tags-row" v-if="selectedTags.length > 0">
							<span class="tag-category-label">帖子类型</span>
							<div class="tags-group">
								<div
									v-for="(tag, index) in selectedTags"
									:key="tag.id"
									class="custom-tag-display"
									:style="getTagDisplayStyle(tag, index)"
									@click="removeTag(tag)"
								>
									{{ tag.name }}
									<span class="tag-close">×</span>
								</div>
							</div>
						</div>
						<div class="tags-row" v-if="selectedSchoolTags.length > 0">
							<span class="tag-category-label">学校名称</span>
							<div class="tags-group">
								<div
									v-for="(tag, index) in selectedSchoolTags"
									:key="tag.id"
									class="custom-tag-display"
									:style="getTagDisplayStyle(tag, index)"
									@click="removeSchoolTag(tag, index)"
								>
									{{ tag.name }}
									<span class="tag-close">×</span>
								</div>
							</div>
						</div>
						<div class="tags-row" v-if="selectedMajorTags.length > 0">
							<span class="tag-category-label">专业方向</span>
							<div class="tags-group">
								<div
									v-for="(tag, index) in selectedMajorTags"
									:key="tag.id"
									class="custom-tag-display"
									:style="getTagDisplayStyle(tag, index)"
									@click="removeMajorTag(tag)"
								>
									{{ tag.name }}
									<span class="tag-close">×</span>
								</div>
							</div>
						</div>
						<el-button
							class="button-new-tag"
							size="small"
							@click="showTagDialog"
						>
							+ 添加标签
						</el-button>
					</div>
				</el-form-item>

				<!-- 标签选择弹窗 -->
				<el-dialog
					v-model="tagDialogVisible"
					title="选择标签"
					width="900px"
					:close-on-click-modal="false"
				>
					<div class="tag-dialog-content">
						<div class="tag-title">添加帖子类型标签</div>
						<div class="tag-list">
							<div class="custom-tag-grid">
								<template v-for="(item, index) in processedTags" :key="item.id">
									<!-- 分隔符 -->
									<div
										v-if="item.type === 'separator'"
										:class="item.breakType === 'double' ? 'double-break' : 'single-break'"
										class="tag-separator"
									></div>

									<!-- 标签 -->
									<div
										v-else-if="item.type === 'tag'"
										class="custom-tag-item"
										:class="{ 'selected': tempSelectedTagIds.includes(item.id) }"
										:style="getTagDialogStyle(item, index, tempSelectedTagIds.includes(item.id))"
										@click="toggleTagSelection(item.id)"
									>
										{{ item.name }}
									</div>
								</template>
							</div>
						</div>

						<div class="tag-title" style="margin-top: 30px;">添加学校名称标签</div>
						<div class="school-tag-section">
							<div class="add-tag">
								<el-select
									v-model="schoolTagInput.id"
									filterable
									remote
									:remote-method="handleSchoolSearch"
									:loading="schoolSearchLoading"
									@change="onSchoolTagChange"
									@clear="onSchoolTagClear"
									@keyup.enter="addSchoolTagsOnEnter"
									placeholder="请输入学校名称标签"
									clearable
									style="width: 100%;"
								>
									<el-option
										v-for="(item, index) in schoolSearchData"
										:key="index"
										:label="item.name + (item.synonyms && item.synonyms.length > 0 ? ',' + item.synonyms.join(',') : '')"
										:value="item.id"
									>
										{{ item.name }}
									</el-option>
								</el-select>
							</div>
							<div class="school-tags-display">
								<div
									v-for="(tag, index) in selectedSchoolTags"
									:key="tag.id"
									class="custom-tag-item school-tag-item"
									:style="getTagDisplayStyle(tag, index)"
								>
									{{ tag.name }}
									<span class="tag-close" @click="removeSchoolTag(tag, index)">×</span>
								</div>
								<p v-if="selectedSchoolTags.length === 0" style="color: #ccc; font-size: 12px; margin: 10px 0;">
									还没有添加学校名称标签...
								</p>
							</div>
						</div>

						<div class="tag-title" style="margin-top: 30px;">添加专业方向标签</div>
						<div class="tag-list">
							<div class="custom-tag-grid">
								<template v-for="(item, index) in processedMajorTags" :key="item.id">
									<!-- 分隔符 -->
									<div
										v-if="item.type === 'separator'"
										:class="item.breakType === 'double' ? 'double-break' : 'single-break'"
										class="tag-separator"
									></div>

									<!-- 标签 -->
									<div
										v-else-if="item.type === 'tag'"
										class="custom-tag-item"
										:class="{ 'selected': tempSelectedMajorTagIds.includes(item.id) }"
										:style="getTagDialogStyle(item, index, tempSelectedMajorTagIds.includes(item.id))"
										@click="toggleMajorTagSelection(item.id)"
									>
										{{ item.name }}
									</div>
								</template>
							</div>
						</div>
					</div>

					<template #footer>
						<span class="dialog-footer">
							<el-button @click="cancelTagSelection">取消</el-button>
							<el-button type="primary" @click="confirmTagSelection">
								确定 (帖子类型: {{ tempSelectedTagIds.length }} 个, 专业方向: {{ tempSelectedMajorTagIds.length }} 个)
							</el-button>
						</span>
					</template>
				</el-dialog>

				<el-form-item style="margin-top: 40px">
					<el-button type="primary" @click="handleSubmit(formRef)">提交</el-button>
					<el-button @click="router.back()">取消</el-button>
				</el-form-item>
			</el-form>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, computed } from "vue";
import { CirclePlus, Delete, Plus, CloseBold } from "@element-plus/icons-vue";
import { forumAccount } from "../../event/data/index";
import type { FormInstance } from "element-plus";
import { ElMessage } from "element-plus";
import { useCool } from "/@/cool";
import { isDev } from "/@/config";
import _ from "lodash";
import Vditor from "vditor";
import "vditor/dist/index.css";

// 类型定义
interface TagItem {
	id: string;
	name: string;
	order?: string;
}

interface NavItem {
	fid: string;
	name: string;
	forums?: NavItem[];
}

interface SubNavItem {
	typeid: string;
	name: string;
}

const { service, router, refs, setRefs } = useCool();

let vd = ref();

const cUrl = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4PortalPic"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4PortalPic";

const form = reactive({
	pic: "",
	title: "",
	summary: "",
	content: "",
	forumTid: "",
	author: "",
	category: "",
	tags: "",
	tagsVal: "",
	majorTags: "",
	majorTagsVal: "",
	schoolTags: "",
	schoolTagsVal: "",
	fid: "",
	typeid: "",
	uid: "",
	username: "",
	thread: false,
	type: 0,
	isExternalLink: false,
	externalLink: "",
	displayOrder: 0,
});

const navs = ref<NavItem[]>([]);
const subNavs = ref<SubNavItem[]>([]);
const availableTags = ref<TagItem[]>([]);
const selectedTags = ref<TagItem[]>([]);
// 处理后的标签数组（包含分隔符）
const processedTags = ref<any[]>([]);

// 专业方向标签相关
const availableMajorTags = ref<TagItem[]>([]);
const selectedMajorTags = ref<TagItem[]>([]);
const processedMajorTags = ref<any[]>([]);

// 标签弹窗相关
const tagDialogVisible = ref(false);
const tempSelectedTagIds = ref<string[]>([]);
const tempSelectedMajorTagIds = ref<string[]>([]);

// 学校名称标签相关
const selectedSchoolTags = ref<TagItem[]>([]);
const schoolSearchData = ref<any[]>([]);
const schoolSearchLoading = ref(false);
const schoolTagInput = ref({
	id: "" as string,
	name: ""
});
const canAddSchoolTag = ref(false);

const rules = reactive({
	pic: [{ required: true, message: "请上传展示图", trigger: "blur" }],
	type: [{ required: true, message: "请选择类型", trigger: "change" }],
	title: [{ required: true, message: "请输入标题", trigger: "blur" }],
	summary: [{ required: true, message: "请输入摘要", trigger: "blur" }],
	content: [{
		required: () => !form.isExternalLink,
		message: "请输入内容",
		trigger: "blur"
	}],
	forumTid: [{
		required: () => !form.thread && !form.isExternalLink,
		message: "请输入帖子ID",
		trigger: "blur"
	}],
	author: [{
		required: () => !form.isExternalLink,
		message: "请输入作者",
		trigger: "blur"
	}],
	category: [{ required: true, message: "请选择分类", trigger: "change" }]
});

const formRef = ref<FormInstance>();

const loadNav = async () => {
	service.base.common.forum
		.nav()
		.then((res) => {
			navs.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const navChange = () => {
	service.base.common.forum
		.subnav({
			fid: form.fid
		})
		.then((res) => {
			subNavs.value = res;
		})
		.catch((err) => {
			ElMessage.error(err.message);
		});
};

const uidChange = (uid) => {
	const account = forumAccount.find((obj) => obj.uid === uid);
	form.username = account?.username || "";
};

const handleExternalLinkChange = () => {
	if (form.isExternalLink) {
		// 当启用外链时，清空相关字段
		form.content = "";
		form.forumTid = "";
		form.author = "";
		form.thread = false;
		// 清空编辑器内容
		if (vd.value) {
			vd.value.setValue("");
		}
	}
	// 取消外链时不需要特殊处理，编辑器会自动恢复正常
};

const initVditor = async () => {
  await nextTick();
  vd.value = new Vditor("vditor", {
    height: 400,
    width: '100%',
    mode: 'sv',
    preview: {
      mode: 'both',
      hljs: {
        style: 'github'
      }
    },
    cache: {
      enable: false
    },
    after: () => {
      vd.value.setValue(form.content);
    },
    input: (value) => {
      form.content = value;
    },
    upload: {
      url: cUrl,
      accept: 'image/*',
      success: (editor: HTMLPreElement, msg: string) => {
        const response = JSON.parse(msg);
        if (response.code === 1000 && response.data.success) {
          const imageUrl = response.data.image;
          // Insert image URL into editor
          vd.value.insertValue(`![image](${imageUrl})`);
        }
      }
    }
  });
};

const handleSubmit = async (formEl: FormInstance | undefined) => {
	if (!formEl) return;
	
	form.content = vd.value.getValue();

	await formEl.validate((isValid, fields) => {
		if (!isValid) return;

		service.base.common.portal
			.portalCreate({
				...form
			})
			.then((res) => {
				ElMessage({
					message: "已添加!",
					type: "success"
				});
				router.back();
			})
			.catch((err) => {
				ElMessage.error(err.message);
			});
	});
};

const handleFileChange = async (event: any, type) => {
	const files = event.target.files;
	if (files) {
		await upload(files, type);
	}

	event.target.value = "";
};

const upload = async (files: any, type) => {
	for (let i = 0; i < files.length; i++) {
		const formData = new FormData();
		formData.append("files", files[i]);

		try {
			const url = isDev
				? "http://localhost:9000/dev/admin/base/open/upload4PortalPic"
				: "https://connect.chasedream.com/api/v2/admin/base/open/upload4PortalPic";

			const response = await fetch(url, {
				method: "POST",
				body: formData
			});

			if (response.ok) {
				const res = await response.json();
				if (type === 1) {
					form.pic = res.data.image;
				} else if (type === 2) {
					form.pic = res.data.image;
				}
			} else {
				ElMessage.error("Upload failed");
			}
		} catch (err: any) {
			ElMessage.error(err.message);
		}
	}
};

const delPic = () => {
	form.pic = "";
};

const addPic = () => {
	const el = refs[`fileInput`];
	el?.click();
};

// 标签相关方法
const loadAvailableTags = async () => {
	try {
		const res = await service.base.common.portal.getAvailableTags({
			property: 3
		});
		// 按 order 字段升序排序
		availableTags.value = res.sort((a: TagItem, b: TagItem) => {
			// 解析版本号，如果为空则设为"3.5.999.999"作为默认排序值
			const parseVersion = (order: string | undefined) => {
				if (!order) return [3, 5, 999, 999]; // 空值放到3.5.x的最后
				const parts = order.split('.').map(Number);
				return [parts[0] || 0, parts[1] || 0, parts[2] || 0, parts[3] || 0];
			};

			const versionA = parseVersion(a.order);
			const versionB = parseVersion(b.order);

			// 先比较第二位，再比较第三位，最后比较第四位
			if (versionA[1] !== versionB[1]) {
				return versionA[1] - versionB[1];
			}
			if (versionA[2] !== versionB[2]) {
				return versionA[2] - versionB[2];
			}
			return versionA[3] - versionB[3];
		});

		// 预处理标签数组，插入分隔符
		processTagsWithSeparators();
	} catch (err: any) {
		ElMessage.error(err.message);
	}
};

// 加载专业方向标签
const loadAvailableMajorTags = async () => {
	try {
		const res = await service.base.common.portal.getAvailableTags({
			property: 2
		});
		// 按 order 字段升序排序
		availableMajorTags.value = res.sort((a: TagItem, b: TagItem) => {
			// 解析版本号，如果为空则设为"3.5.999.999"作为默认排序值
			const parseVersion = (order: string | undefined) => {
				if (!order) return [3, 5, 999, 999]; // 空值放到3.5.x的最后
				const parts = order.split('.').map(Number);
				return [parts[0] || 0, parts[1] || 0, parts[2] || 0, parts[3] || 0];
			};

			const versionA = parseVersion(a.order);
			const versionB = parseVersion(b.order);

			// 先比较第二位，再比较第三位，最后比较第四位
			if (versionA[1] !== versionB[1]) {
				return versionA[1] - versionB[1];
			}
			if (versionA[2] !== versionB[2]) {
				return versionA[2] - versionB[2];
			}
			return versionA[3] - versionB[3];
		});

		// 预处理专业方向标签数组，插入分隔符
		processMajorTagsWithSeparators();
	} catch (err: any) {
		ElMessage.error(err.message);
	}
};

// 预处理标签数组，在需要换行的位置插入分隔符
const processTagsWithSeparators = () => {
	const processed: any[] = [];
	const encounteredVersions = new Set<string>();
	
	for (const tag of availableTags.value) {
		let shouldAddSeparator = false;
		let separatorType = '';
		
		if (tag.order) {
			// 检查是否需要添加分隔符
			// 3.3版本识别：order="3.3" 或 order="3.3.x" 格式
			if ((tag.order === "3.3" || tag.order.match(/^3\.3\.\d+$/)) && !encounteredVersions.has('3.3')) {
				console.log('3.3版本识别:', tag.order, '-> 双换行');
				shouldAddSeparator = true;
				separatorType = 'double';
				encounteredVersions.add('3.3');
			}
			// 3.5.x版本识别：order="3.5.x" 格式（如3.5.1, 3.5.2等）
			else if (tag.order.match(/^3\.5\.\d+$/) && !encounteredVersions.has('3.5')) {
				console.log('3.5.x版本识别:', tag.order, '-> 单换行');
				shouldAddSeparator = true;
				separatorType = 'single';
				encounteredVersions.add('3.5');
			}
			// 3.6.x版本识别：order="3.6.x" 格式（如3.6.1, 3.6.2等）
			else if (tag.order.match(/^3\.6\.\d+$/) && !encounteredVersions.has('3.6')) {
				console.log('3.6.x版本识别:', tag.order, '-> 单换行');
				shouldAddSeparator = true;
				separatorType = 'single';
				encounteredVersions.add('3.6');
			}
		}
		
		// 如果需要分隔符，先添加分隔符对象
		if (shouldAddSeparator) {
			processed.push({
				type: 'separator',
				breakType: separatorType,
				id: `separator-${processed.length}`
			});
		}
		
		// 添加标签对象
		processed.push({
			type: 'tag',
			...tag
		});
	}
	
	processedTags.value = processed;
};

// 预处理专业方向标签数组，在需要换行的位置插入分隔符
const processMajorTagsWithSeparators = () => {
	const processed: any[] = [];
	const encounteredVersions = new Set<string>();

	for (const tag of availableMajorTags.value) {
		let shouldAddSeparator = false;
		let separatorType = '';

		if (tag.order) {
			// 3.5.x版本识别：order="3.5.x" 格式（如3.5.1, 3.5.2等）
			if (tag.order.match(/^3\.5\.\d+$/) && !encounteredVersions.has('3.5')) {
				console.log('专业方向3.5.x版本识别:', tag.order, '-> 双换行');
				shouldAddSeparator = true;
				separatorType = 'double';
				encounteredVersions.add('3.5');
			}
			// 3.6.x版本识别：order="3.6.x" 格式（如3.6.1, 3.6.2等）
			else if (tag.order.match(/^3\.6\.\d+$/) && !encounteredVersions.has('3.6')) {
				console.log('专业方向3.6.x版本识别:', tag.order, '-> 单换行');
				shouldAddSeparator = true;
				separatorType = 'single';
				encounteredVersions.add('3.6');
			}
		}

		// 如果需要分隔符，先添加分隔符对象
		if (shouldAddSeparator) {
			processed.push({
				type: 'separator',
				breakType: separatorType,
				id: `separator-${processed.length}`
			});
		}

		// 添加标签对象
		processed.push({
			type: 'tag',
			...tag
		});
	}

	processedMajorTags.value = processed;
};

// 显示标签选择弹窗
const showTagDialog = () => {
	// 初始化临时选中的标签ID
	tempSelectedTagIds.value = selectedTags.value.map(tag => tag.id);
	tempSelectedMajorTagIds.value = selectedMajorTags.value.map(tag => tag.id);
	tagDialogVisible.value = true;
};

// 取消标签选择
const cancelTagSelection = () => {
	tagDialogVisible.value = false;
	tempSelectedTagIds.value = [];
	tempSelectedMajorTagIds.value = [];
};

// 确认标签选择
const confirmTagSelection = () => {
	// 根据选中的ID更新selectedTags
	selectedTags.value = availableTags.value.filter(tag =>
		tempSelectedTagIds.value.includes(tag.id)
	);
	// 根据选中的ID更新selectedMajorTags
	selectedMajorTags.value = availableMajorTags.value.filter(tag =>
		tempSelectedMajorTagIds.value.includes(tag.id)
	);
	updateFormTags();
	updateFormMajorTags();
	tagDialogVisible.value = false;
	tempSelectedTagIds.value = [];
	tempSelectedMajorTagIds.value = [];
};

// 切换标签选择状态
const toggleTagSelection = (tagId: string) => {
	const index = tempSelectedTagIds.value.indexOf(tagId);
	if (index > -1) {
		tempSelectedTagIds.value.splice(index, 1);
	} else {
		tempSelectedTagIds.value.push(tagId);
	}
};

// 切换专业方向标签选择状态
const toggleMajorTagSelection = (tagId: string) => {
	const index = tempSelectedMajorTagIds.value.indexOf(tagId);
	if (index > -1) {
		tempSelectedMajorTagIds.value.splice(index, 1);
	} else {
		tempSelectedMajorTagIds.value.push(tagId);
	}
};

const removeTag = (tag: any) => {
	const index = selectedTags.value.findIndex(t => t.id === tag.id);
	if (index > -1) {
		selectedTags.value.splice(index, 1);
		updateFormTags();
	}
};

const removeMajorTag = (tag: any) => {
	const index = selectedMajorTags.value.findIndex(t => t.id === tag.id);
	if (index > -1) {
		selectedMajorTags.value.splice(index, 1);
		updateFormMajorTags();
	}
};

const isTagSelected = (tagId: string) => {
	return selectedTags.value.some(tag => tag.id === tagId);
};

const updateFormTags = () => {
	form.tags = selectedTags.value.map(tag => tag.name).join("|");
	form.tagsVal = selectedTags.value.map(tag => tag.id).join("|");
};

const updateFormMajorTags = () => {
	form.majorTags = selectedMajorTags.value.map(tag => tag.name).join("|");
	form.majorTagsVal = selectedMajorTags.value.map(tag => tag.id).join("|");
};

const updateFormSchoolTags = () => {
	form.schoolTags = selectedSchoolTags.value.map(tag => tag.name).join("|");
	form.schoolTagsVal = selectedSchoolTags.value.map(tag => tag.id).join("|");
};

// 颜色生成相关函数
const tagColors = [
	'#d32f2f', // bg1: 红色
	'#9c3dc4', // bg2: 紫色
	'#673ab7', // bg3: 深紫色
	'#3f51b5', // bg4: 靛蓝色
	'#2196f3', // bg5: 蓝色
	'#00a9bb', // bg6: 青色
	'#007467', // bg7: 蓝绿色
	'#29892d', // bg8: 绿色
	'#8bc34a', // bg9: 浅绿色
	'#afb42b', // bg10: 橄榄色
	'#ffbf00', // bg11: 琥珀色
	'#f88a1a', // bg12: 橙色
	'#ff5722', // bg13: 深橙色
	'#795548', // bg14: 棕色
	'#c2c2c2', // bg15: 灰色
];

// 基于标签索引生成颜色索引，确保相邻标签颜色不同
const getTagColorIndex = (index: number): number => {
	return index % tagColors.length;
};

// 获取标签在表单中显示的样式
const getTagDisplayStyle = (tag: TagItem, index: number) => {
	const colorIndex = getTagColorIndex(index);
	const color = tagColors[colorIndex];
	return {
		backgroundColor: color,
		color: '#fff',
		border: `1px solid ${color}`,
	};
};

// 获取标签在弹窗中的样式
const getTagDialogStyle = (tag: TagItem, index: number, isSelected: boolean) => {
	const colorIndex = getTagColorIndex(index);
	const color = tagColors[colorIndex];
	
	if (isSelected) {
		// 已选中状态：实心长方形 - 有色背景 + 白色文字
		return {
			backgroundColor: color,
			color: '#fff',
			border: `1px solid ${color}`,
		};
	} else {
		// 未选中状态：空心长方形 - 透明背景 + 有色边框 + 有色文字
		return {
			backgroundColor: 'transparent',
			color: color,
			border: `1px solid ${color}`,
		};
	}
};

// 学校名称标签相关方法
const handleSchoolSearch = async (query: string) => {
	schoolSearchData.value = [];
	if (query !== "") {
		schoolSearchLoading.value = true;
		try {
			const res = await service.base.common.portal.tagSearch({
				s: query,
				property: 1
			});
			if (res && res.length > 0) {
				schoolSearchData.value = res.map((item: any) => ({
					...item,
					synonyms: item.synonym ? item.synonym.map((s: any) => s.keyword || s.name) : []
				}));
			}
		} catch (err: any) {
			ElMessage.error(err.message || "搜索失败");
		} finally {
			schoolSearchLoading.value = false;
		}
	}
};

const onSchoolTagChange = (tagId: string) => {
	if (tagId) {
		canAddSchoolTag.value = true;
		// 自动添加选择的标签
		addSchoolTagById(tagId);
		// 清空选择，保持输入框为空
		schoolTagInput.value.id = "";
	} else {
		canAddSchoolTag.value = false;
	}
};

const addSchoolTagsOnEnter = () => {
	if (schoolTagInput.value.id) {
		const tagId = schoolTagInput.value.id;
		addSchoolTagById(tagId);
		// 清空输入
		onSchoolTagClear();
	}
};

const addSchoolTagById = (tagId: string) => {
	const existingTag = schoolSearchData.value.find(item => item.id === tagId);

	if (existingTag) {
		// 检查是否已经添加过
		const isAlreadyAdded = selectedSchoolTags.value.some(tag => tag.id === tagId);
		if (!isAlreadyAdded) {
			selectedSchoolTags.value.push({
				id: existingTag.id,
				name: existingTag.name
			});
			updateFormSchoolTags();
		} else {
			ElMessage.warning("该学校标签已经添加过了！");
		}
	}
};

const onSchoolTagClear = () => {
	schoolTagInput.value.id = "";
	schoolTagInput.value.name = "";
	canAddSchoolTag.value = false;
	schoolSearchData.value = [];
};



const removeSchoolTag = (_tag: TagItem, index: number) => {
	selectedSchoolTags.value.splice(index, 1);
	updateFormSchoolTags();
};


onMounted(() => {
	loadNav();
	loadAvailableTags();
	loadAvailableMajorTags();
    initVditor();
});

</script>

<style lang="scss" scoped>
@import "../css/index.scss";

.tags-container {
	display: flex;
	flex-wrap: wrap;
	gap: 8px;
	align-items: center;

	.custom-tag-display {
		width: 60px;
		height: 23px;
		border: 1px solid;
		border-radius: 0;
		cursor: pointer;
		font-size: 12px;
		line-height: 1;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		padding: 0 2px;
		box-sizing: border-box;
		position: relative;

		.tag-close {
			display: none;
			position: absolute;
			right: 2px;
			top: 50%;
			transform: translateY(-50%);
			font-size: 12px;
			line-height: 1;
			opacity: 0.7;
		}

		&:hover .tag-close {
			display: block;
		}
	}

	.tag-item {
		margin-bottom: 4px;
	}

	.button-new-tag {
		height: 24px;
		padding: 0 8px;
		font-size: 12px;
		border-style: dashed;
	}
}

.tags-container-combined {
	.tags-row {
		display: flex;
		align-items: center;
		gap: 8px;
		margin-bottom: 8px;

		&:last-child {
			margin-bottom: 0;
		}

		.tag-category-label {
			min-width: 80px;
			font-size: 14px;
			color: #606266;
			font-weight: 500;
		}

		.tags-group {
			display: flex;
			flex-wrap: wrap;
			gap: 8px;
			align-items: center;
			flex: 1;
		}

		.custom-tag-display {
			width: 60px;
			height: 23px;
			border: 1px solid;
			border-radius: 0;
			cursor: pointer;
			font-size: 12px;
			line-height: 1;
			text-align: center;
			display: flex;
			align-items: center;
			justify-content: center;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			padding: 0 2px;
			box-sizing: border-box;
			position: relative;

			.tag-close {
				display: none;
				position: absolute;
				right: 2px;
				top: 50%;
				transform: translateY(-50%);
				font-size: 12px;
				line-height: 1;
				opacity: 0.7;
			}

			&:hover .tag-close {
				display: block;
			}
		}

		.button-new-tag {
			height: 24px;
			padding: 0 8px;
			font-size: 12px;
			border-style: dashed;
			margin-left: auto;
		}
	}
}

.tag-dialog-content {
	.tag-title {
		margin-bottom: 5px;
	}
	.tag-list {
		max-height: 400px;
		overflow-y: auto;
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		padding: 12px;
		
		.custom-tag-grid {
			display: flex;
			flex-wrap: wrap;
			justify-content: flex-start;
			
			.tag-separator {
				width: 100%;
				flex-basis: 100%; // 强制换行
				height: 0;
				
				&.single-break {
					margin-bottom: 0; // 单换行
				}
				
				&.double-break {
					margin-bottom: 12px; // 双换行（适中间距）
				}
			}
			
			.custom-tag-item {
				width: 60px;
				height: 23px;
				border: 1px solid;
				border-radius: 0;
				cursor: pointer;
				font-size: 12px;
				line-height: 1;
				text-align: center;
				transition: all 0.2s ease;
				user-select: none;
				display: flex;
				align-items: center;
				justify-content: center;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				padding: 0 4px;
				box-sizing: border-box;
				margin-right: 8px; // 只控制标签之间的水平间距
				margin-bottom: 8px; // 控制垂直间距
				
				&:hover {
					transform: scale(1.02);
					box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
				}
			}
		}
	}
}

.dialog-footer {
	display: flex;
	justify-content: flex-end;
	gap: 12px;
}

.school-tag-section {
	.add-tag {
		margin-bottom: 12px;
	}

	.school-tags-display {
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		padding: 12px;
		min-height: 60px;
		display: flex;
		flex-wrap: wrap;
		gap: 8px;
		align-items: flex-start;

		.school-tag-item {
			position: relative;
			padding-right: 18px; // 为删除按钮留出空间

			.tag-close {
				display: flex;
				align-items: center;
				justify-content: center;
				position: absolute;
				right: -8px;
				top: -8px;
				width: 16px;
				height: 16px;
				background: #fff;
				border-radius: 50%;
				font-size: 12px;
				line-height: 1;
				cursor: pointer;
				color: #ff4444;
				font-weight: bold;
				border: 1px solid #e0e0e0;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
				transition: all 0.2s ease;
				z-index: 10;

				&:hover {
					background: #fff;
					color: #ff0000;
					border-color: #ff4444;
					transform: scale(1.1);
					box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
				}

				&:active {
					transform: scale(0.95);
				}
			}
		}
	}
}

.external-link-overlay {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(245, 247, 250, 0.9);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 1000;
	border-radius: 4px;
}

.external-link-overlay span {
	background-color: #fff;
	padding: 8px 16px;
	border-radius: 4px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	color: #666;
	font-size: 14px;
}

.vditor-container {
	position: relative;
}
</style>
